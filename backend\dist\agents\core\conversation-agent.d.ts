import { DataSource, EntityManager } from 'typeorm';
import { Agent, type Message as AgentMessage } from '../../../../ai-agent/dist';
import { Agent as AgentEntity } from '../agent.entity';
export declare function createDefaultAgent(db: DataSource | EntityManager, userId: string): Promise<{
    name: string;
} & AgentEntity>;
type BigIntLike = bigint | string | number;
export declare enum ConversationNotification {
    AgentIsThinking = "AgentIsThinking",
    AgentIsGeneratingResponse = "AgentIsGeneratingResponse",
    None = "None"
}
export interface AgentRuntimeCallbacks {
    setNotification?: (conversationUuid: string, notification: ConversationNotification) => Promise<void> | void;
}
export interface ProcessUserMessageInput {
    conversationUuid: string;
    userMessage: string;
    ownerName: string;
    agentId: bigint;
}
export interface GenerateMessageInput {
    conversationUuid: string;
    agentId: bigint;
    ownerName: string;
}
export declare function toBigInt(id: BigIntLike): bigint;
export declare function mapLanguageCodeToName(languageCode: string): string;
export declare function buildConversationAgent(db: DataSource, conversationUuid: string, storeCurrency: string, preferredLanguage?: string): Agent;
export declare function buildProductContextMessage(products: Array<{
    id: bigint;
    name: string;
    description: string | null;
    price: number;
    sku: string | null;
}>, storeCurrency: string): AgentMessage | null;
export declare function buildCustomerContextMessage(customer: {
    name: string | null;
    email: string | null;
    phone: string | null;
    address: string | null;
} | null): AgentMessage | null;
export declare function ensureLLMProviderConfigured(): Promise<void>;
export {};
